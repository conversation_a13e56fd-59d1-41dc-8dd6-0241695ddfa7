# Vector File ExifTool Metadata Embedding

## Overview

Meta Master now supports metadata embedding into vector files using ExifTool, providing an alternative to Adobe Illustrator-based embedding. This feature allows for faster, more automated metadata embedding into supported vector formats.

## Supported Vector Formats

### ✅ Fully Supported (Read/Write)
- **EPS** (.eps) - Encapsulated PostScript files
- **AI** (.ai) - Adobe Illustrator files

### ⚠️ Read-Only
- **SVG** (.svg) - Scalable Vector Graphics files (ExifTool limitation)

## Features

### 🔧 ExifTool Integration
- Uses the same ExifTool engine as image metadata embedding
- Supports XMP, IPTC, and format-specific metadata tags
- Automatic character encoding handling (UTF-8)
- Hidden console execution for seamless user experience

### 🤖 Auto-Embed Support
- Vector files now support auto-embed functionality
- Works in Vector mode alongside existing Image mode
- Automatic file type detection and validation
- Graceful handling of unsupported formats

### 📋 Metadata Tags Embedded

For **EPS and AI files**:
- `XMP-dc:Title` - Document title
- `XMP-dc:Description` - Document description  
- `XMP-dc:subject` - Keywords/subjects
- `XMP-dc:creator` - Creator information
- `XMP-dc:rights` - Rights/copyright
- `XMP-dc:publisher` - Publisher (Meta Master)
- `XMP-xmp:CreatorTool` - Creation tool (Meta Master)
- `EPS:Title` - EPS-specific title tag
- `EPS:Creator` - EPS-specific creator tag
- `keywords` - Generic keywords
- `IPTC:Keywords` - IPTC keyword tags

## Usage

### Manual Embedding
1. Load vector files (.eps, .ai) into Meta Master
2. Generate or edit metadata in the tree view
3. Click "Embed Image" button
4. Vector files will be processed using ExifTool

### Auto-Embed
1. Enable "Auto Embed" toggle
2. Set mode to "Vector" or "Image" 
3. Process vector files - metadata will be automatically embedded
4. Works with both single file and batch processing

### File Type Handling
- **EPS/AI files**: Full metadata embedding support
- **SVG files**: Automatically detected and redirected to Illustrator embedding
- **Other formats**: Clear error messages with format guidance

## Technical Implementation

### File Type Detection
```python
file_ext = os.path.splitext(file_path)[1].lower()
is_vector_file = file_ext in ['.eps', '.ai', '.svg']
is_writable_vector = file_ext in ['.eps', '.ai']  # SVG is read-only
```

### ExifTool Command Structure
```python
command = [
    get_exiftool_path(),
    '-overwrite_original',
    '-preserve',
    '-codedcharacterset=utf8',
    '-charset', 'iptc=utf8',
    '-charset', 'exif=utf8',
    f'-XMP-dc:Title={title}',
    f'-XMP-dc:Description={description}',
    # ... additional tags
    file_path
]
```

### Error Handling
- SVG files are gracefully rejected with helpful error messages
- Timeout protection (30 seconds) prevents hanging
- Comprehensive validation of inputs and file existence
- Detailed logging for troubleshooting

## Benefits

### 🚀 Performance
- Faster than Illustrator COM interface
- No need to launch Adobe Illustrator
- Batch processing efficiency
- Reduced memory usage

### 🔄 Consistency
- Same metadata format as image files
- Unified embedding workflow
- Consistent error handling
- Standardized metadata tags

### 🛠️ Reliability
- ExifTool's proven metadata handling
- Cross-platform compatibility
- Robust error recovery
- Extensive format support

## Limitations

### SVG Files
- ExifTool does not support writing to SVG files
- SVG files are automatically redirected to Illustrator embedding
- Read-only access for metadata extraction

### Format-Specific
- Some vector-specific metadata may not be preserved
- Complex vector elements are not affected
- Embedded images within vectors are not processed

## Testing

Use the provided test scripts to verify functionality:

```bash
# Test ExifTool vector support
python test_vector_exiftool_embedding.py

# Test auto-embed integration  
python test_vector_auto_embed.py
```

## Migration from Illustrator Embedding

### Advantages of ExifTool Embedding
- No Adobe Illustrator dependency
- Faster processing
- Better batch performance
- More reliable automation

### When to Use Illustrator Embedding
- SVG files (required)
- Complex vector-specific metadata
- EPS version compatibility requirements
- Advanced vector processing needs

## Configuration

The vector embedding behavior can be controlled through existing settings:

- **Auto Embed Toggle**: Enables/disables automatic embedding
- **Vector Mode**: Activates vector file processing
- **Description Settings**: Controls description embedding
- **Keyword Settings**: Manages keyword processing

## Future Enhancements

### Planned Improvements
- SVG metadata embedding via alternative methods
- Additional vector format support
- Enhanced metadata validation
- Performance optimizations

### Potential Additions
- Vector-specific metadata fields
- Custom tag mapping
- Batch conversion utilities
- Metadata extraction tools

---

## Summary

The ExifTool vector embedding feature provides a robust, efficient alternative for metadata embedding into EPS and AI files. While SVG files remain read-only, the system gracefully handles all vector formats and provides clear guidance for users. This enhancement significantly improves the workflow for vector file metadata management in Meta Master.
