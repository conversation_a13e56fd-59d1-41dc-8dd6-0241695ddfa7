#!/usr/bin/env python3
"""
Test script to verify mouse wheel scrolling in the Meta Master settings window.
This creates a minimal test window with the same scrolling implementation.
"""

import tkinter as tk
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import sys
import os

def create_test_settings_window():
    """Create a test settings window with scrolling to verify mouse wheel functionality."""
    
    # Create main window
    root = ttk.Window(themename="darkly")
    root.title("Settings Window Scroll Test")
    root.geometry("700x500")
    root.resizable(True, True)
    
    # Create main frame with scrolling (same as Meta Master implementation)
    main_container = ttk.Frame(root)
    main_container.pack(fill="both", expand=True)

    # Add a canvas for scrolling
    canvas = tk.Canvas(main_container)
    scrollbar = ttk.Scrollbar(main_container, orient="vertical", command=canvas.yview)
    scrollable_frame = ttk.Frame(canvas)

    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )

    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)

    # Add mouse wheel scrolling support (cross-platform)
    def on_mousewheel(event):
        """Handle mouse wheel scrolling for Windows and macOS."""
        canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
    
    def on_mousewheel_linux(event):
        """Handle mouse wheel scrolling for Linux."""
        if event.num == 4:
            canvas.yview_scroll(-1, "units")
        elif event.num == 5:
            canvas.yview_scroll(1, "units")
    
    def bind_mousewheel(event):
        """Bind mouse wheel events when mouse enters the canvas."""
        canvas.bind_all("<MouseWheel>", on_mousewheel)  # Windows and macOS
        canvas.bind_all("<Button-4>", on_mousewheel_linux)  # Linux scroll up
        canvas.bind_all("<Button-5>", on_mousewheel_linux)  # Linux scroll down
    
    def unbind_mousewheel(event):
        """Unbind mouse wheel events when mouse leaves the canvas."""
        canvas.unbind_all("<MouseWheel>")
        canvas.unbind_all("<Button-4>")
        canvas.unbind_all("<Button-5>")
    
    # Bind mouse wheel events to canvas and scrollable frame
    canvas.bind('<Enter>', bind_mousewheel)
    canvas.bind('<Leave>', unbind_mousewheel)
    scrollable_frame.bind('<Enter>', bind_mousewheel)
    scrollable_frame.bind('<Leave>', unbind_mousewheel)

    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # Add test content to make scrolling necessary
    create_test_content(scrollable_frame)
    
    # Add instructions
    instructions = ttk.Label(
        root,
        text="🖱️ Test mouse wheel scrolling by hovering over the content and using your mouse wheel",
        foreground="yellow",
        font=("Segoe UI", 10, "bold")
    )
    instructions.pack(side="bottom", pady=5)
    
    return root

def create_test_content(parent):
    """Create test content similar to the settings window to test scrolling."""
    
    # Title section
    title_section = ttk.LabelFrame(parent, text="Title Settings", padding="10")
    title_section.pack(fill="x", padx=10, pady=5)
    
    for i in range(3):
        label = ttk.Label(title_section, text=f"Title Setting {i+1}:")
        label.pack(anchor="w", pady=2)
        
        scale = ttk.Scale(title_section, from_=0, to=100, length=400)
        scale.pack(fill="x", pady=2)
    
    # Keywords section
    keywords_section = ttk.LabelFrame(parent, text="Keywords Settings", padding="10")
    keywords_section.pack(fill="x", padx=10, pady=5)
    
    for i in range(3):
        label = ttk.Label(keywords_section, text=f"Keywords Setting {i+1}:")
        label.pack(anchor="w", pady=2)
        
        scale = ttk.Scale(keywords_section, from_=0, to=100, length=400)
        scale.pack(fill="x", pady=2)
    
    # Description section
    description_section = ttk.LabelFrame(parent, text="Description Settings", padding="10")
    description_section.pack(fill="x", padx=10, pady=5)
    
    for i in range(3):
        label = ttk.Label(description_section, text=f"Description Setting {i+1}:")
        label.pack(anchor="w", pady=2)
        
        scale = ttk.Scale(description_section, from_=0, to=100, length=400)
        scale.pack(fill="x", pady=2)
    
    # Processing section
    processing_section = ttk.LabelFrame(parent, text="Processing Settings", padding="10")
    processing_section.pack(fill="x", padx=10, pady=5)
    
    for i in range(5):
        check = ttk.Checkbutton(processing_section, text=f"Processing Option {i+1}")
        check.pack(anchor="w", pady=2)
    
    # Custom Prompt section
    prompt_section = ttk.LabelFrame(parent, text="Custom Prompt Settings", padding="10")
    prompt_section.pack(fill="x", padx=10, pady=5)
    
    text_widget = tk.Text(prompt_section, height=6, width=60, wrap=tk.WORD)
    text_widget.pack(fill="x", pady=5)
    text_widget.insert("1.0", "This is a test text area to demonstrate scrolling functionality. " * 10)
    
    # Vector section
    vector_section = ttk.LabelFrame(parent, text="Vector Settings", padding="10")
    vector_section.pack(fill="x", padx=10, pady=5)
    
    for i in range(4):
        radio = ttk.Radiobutton(vector_section, text=f"Vector Option {i+1}")
        radio.pack(anchor="w", pady=2)
    
    # Custom Words section
    words_section = ttk.LabelFrame(parent, text="Custom Words", padding="10")
    words_section.pack(fill="x", padx=10, pady=5)
    
    words_text = tk.Text(words_section, height=4, width=60, wrap=tk.WORD)
    words_text.pack(fill="x", pady=5)
    words_text.insert("1.0", "custom, words, test, scrolling, functionality")
    
    # Negative Words section
    negative_section = ttk.LabelFrame(parent, text="Negative Words", padding="10")
    negative_section.pack(fill="x", padx=10, pady=5)
    
    negative_text = tk.Text(negative_section, height=4, width=60, wrap=tk.WORD)
    negative_text.pack(fill="x", pady=5)
    negative_text.insert("1.0", "negative, words, to, exclude, from, generation")
    
    # Buttons section
    buttons_section = ttk.LabelFrame(parent, text="Actions", padding="10")
    buttons_section.pack(fill="x", padx=10, pady=5)
    
    button_frame = ttk.Frame(buttons_section)
    button_frame.pack(fill="x")
    
    save_btn = ttk.Button(button_frame, text="Save Settings", bootstyle=SUCCESS)
    save_btn.pack(side="left", padx=5, pady=5)
    
    reset_btn = ttk.Button(button_frame, text="Reset to Defaults", bootstyle=WARNING)
    reset_btn.pack(side="left", padx=5, pady=5)
    
    cancel_btn = ttk.Button(button_frame, text="Cancel", bootstyle=DANGER)
    cancel_btn.pack(side="left", padx=5, pady=5)
    
    # Add some extra content to ensure scrolling is needed
    for i in range(5):
        extra_section = ttk.LabelFrame(parent, text=f"Extra Section {i+1}", padding="10")
        extra_section.pack(fill="x", padx=10, pady=5)
        
        extra_label = ttk.Label(extra_section, text=f"This is extra content section {i+1} to ensure the window needs scrolling.")
        extra_label.pack(anchor="w", pady=5)
        
        extra_scale = ttk.Scale(extra_section, from_=0, to=100, length=400)
        extra_scale.pack(fill="x", pady=2)

def test_scroll_functionality():
    """Test the scroll functionality."""
    print("🚀 Testing Settings Window Mouse Wheel Scrolling")
    print("=" * 50)
    
    print("📋 Test Instructions:")
    print("1. A test settings window will open")
    print("2. Hover your mouse over the content area")
    print("3. Use your mouse wheel to scroll up and down")
    print("4. Verify that the content scrolls smoothly")
    print("5. Test both inside and outside the scrollable area")
    print("6. Close the window when done testing")
    
    print("\n🔧 Expected Behavior:")
    print("✅ Mouse wheel should scroll the content when hovering over it")
    print("✅ Scrolling should work on Windows, macOS, and Linux")
    print("✅ Scrollbar should also work for manual scrolling")
    print("✅ Mouse wheel should only work when hovering over the content")
    
    print("\n🖱️ Opening test window...")
    
    try:
        root = create_test_settings_window()
        
        print("✅ Test window created successfully!")
        print("🎯 Use your mouse wheel to test scrolling functionality")
        
        # Start the GUI
        root.mainloop()
        
        print("✅ Test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error creating test window: {e}")
        return False

def main():
    """Main test function."""
    print("🖱️ Settings Window Mouse Wheel Scroll Test")
    print("=" * 50)
    
    success = test_scroll_functionality()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Mouse wheel scrolling test completed!")
        print("🎉 The settings window should now support mouse wheel scrolling")
        print("\n📋 Features added:")
        print("   • Cross-platform mouse wheel support")
        print("   • Windows/macOS: <MouseWheel> event")
        print("   • Linux: <Button-4> and <Button-5> events")
        print("   • Smart binding/unbinding on mouse enter/leave")
        print("   • Smooth scrolling experience")
    else:
        print("❌ Mouse wheel scrolling test failed!")
        print("⚠️ Check the error messages above for details")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
