#!/usr/bin/env python3
"""
Test script to verify ExifTool can embed metadata into vector files.
This script tests the new vector file metadata embedding functionality.
"""

import os
import sys
import subprocess
import tempfile
from pathlib import Path

def get_exiftool_path():
    """Get the full path to exiftool.exe."""
    if getattr(sys, 'frozen', False):
        exe_path = os.path.join(sys._MEIPASS, "exiftool.exe")
        os.environ["EXIFTOOL_HOME"] = os.path.join(sys._MEIPASS, "exiftool_files")
        return exe_path
    else:
        exe_path = os.path.abspath("exiftool.exe")
        os.environ["EXIFTOOL_HOME"] = os.path.abspath("exiftool_files")
        return exe_path

def create_test_svg():
    """Create a simple test SVG file."""
    svg_content = '''<?xml version="1.0" encoding="UTF-8"?>
<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
  <circle cx="50" cy="50" r="40" stroke="black" stroke-width="3" fill="red" />
  <text x="50" y="55" text-anchor="middle" fill="white" font-family="Arial" font-size="12">Test</text>
</svg>'''
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.svg', delete=False, encoding='utf-8') as f:
        f.write(svg_content)
        return f.name

def create_test_eps():
    """Create a simple test EPS file."""
    eps_content = '''%!PS-Adobe-3.0 EPSF-3.0
%%BoundingBox: 0 0 100 100
%%Creator: Test Script
%%Title: Test EPS File
%%EndComments
newpath
50 50 40 0 360 arc
1 0 0 setrgbcolor
fill
showpage
%%EOF'''
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.eps', delete=False, encoding='utf-8') as f:
        f.write(eps_content)
        return f.name

def test_exiftool_vector_support():
    """Test if ExifTool can read vector file formats."""
    print("🔧 Testing ExifTool Vector Format Support...")
    
    exiftool_path = get_exiftool_path()
    
    if not os.path.exists(exiftool_path):
        print(f"❌ ExifTool not found at: {exiftool_path}")
        return False
    
    # Test supported formats
    test_command = [exiftool_path, "-listf"]
    
    try:
        result = subprocess.run(
            test_command,
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            supported_formats = result.stdout.upper()
            vector_formats = {
                'SVG': 'SVG' in supported_formats,
                'EPS': 'EPS' in supported_formats,
                'AI': 'AI' in supported_formats
            }
            
            print("📋 Vector Format Support:")
            for fmt, supported in vector_formats.items():
                status = "✅" if supported else "❌"
                print(f"   {status} {fmt}: {'Supported' if supported else 'Not supported'}")
            
            return any(vector_formats.values())
        else:
            print(f"❌ ExifTool command failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing ExifTool: {e}")
        return False

def test_vector_metadata_embedding():
    """Test metadata embedding into vector files."""
    print("\n🔧 Testing Vector Metadata Embedding...")
    
    exiftool_path = get_exiftool_path()
    
    # Test data
    title = "Test Vector Title"
    description = "Test vector description for metadata embedding"
    keywords = ["vector", "test", "metadata", "svg"]
    
    # Test SVG embedding
    print("\n📄 Testing SVG metadata embedding...")
    svg_file = create_test_svg()
    
    try:
        # Build ExifTool command for SVG
        command = [
            exiftool_path,
            '-overwrite_original',
            '-preserve',
            '-codedcharacterset=utf8',
            f'-XMP-dc:Title={title}',
            f'-XMP-dc:Description={description}',
        ]
        
        # Add keywords
        for keyword in keywords:
            command.extend([
                f'-XMP-dc:subject+={keyword}',
                f'-keywords+={keyword}',
            ])
        
        command.append(svg_file)
        
        # Run ExifTool
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            print("✅ SVG metadata embedding successful!")
            
            # Verify metadata was written
            verify_command = [
                exiftool_path,
                '-s',
                '-G',
                '-XMP-dc:Title',
                '-XMP-dc:Description',
                '-XMP-dc:subject',
                svg_file
            ]
            
            verify_result = subprocess.run(
                verify_command,
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if verify_result.returncode == 0 and verify_result.stdout:
                print("✅ SVG metadata verification successful!")
                print("📋 Embedded metadata:")
                for line in verify_result.stdout.strip().split('\n'):
                    if line.strip():
                        print(f"   {line}")
                svg_success = True
            else:
                print("⚠️ SVG metadata verification failed or no metadata found")
                svg_success = False
        else:
            print(f"❌ SVG metadata embedding failed: {result.stderr}")
            svg_success = False
            
    except Exception as e:
        print(f"❌ SVG test error: {e}")
        svg_success = False
    finally:
        # Clean up
        try:
            os.unlink(svg_file)
        except:
            pass
    
    # Test EPS embedding
    print("\n📄 Testing EPS metadata embedding...")
    eps_file = create_test_eps()
    
    try:
        # Build ExifTool command for EPS
        command = [
            exiftool_path,
            '-overwrite_original',
            '-preserve',
            '-codedcharacterset=utf8',
            f'-XMP-dc:Title={title}',
            f'-XMP-dc:Description={description}',
            f'-EPS:Title={title}',
        ]
        
        # Add keywords
        for keyword in keywords:
            command.extend([
                f'-XMP-dc:subject+={keyword}',
                f'-keywords+={keyword}',
            ])
        
        command.append(eps_file)
        
        # Run ExifTool
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            print("✅ EPS metadata embedding successful!")
            
            # Verify metadata was written
            verify_command = [
                exiftool_path,
                '-s',
                '-G',
                '-XMP-dc:Title',
                '-EPS:Title',
                '-keywords',
                eps_file
            ]
            
            verify_result = subprocess.run(
                verify_command,
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if verify_result.returncode == 0 and verify_result.stdout:
                print("✅ EPS metadata verification successful!")
                print("📋 Embedded metadata:")
                for line in verify_result.stdout.strip().split('\n'):
                    if line.strip():
                        print(f"   {line}")
                eps_success = True
            else:
                print("⚠️ EPS metadata verification failed or no metadata found")
                eps_success = False
        else:
            print(f"❌ EPS metadata embedding failed: {result.stderr}")
            eps_success = False
            
    except Exception as e:
        print(f"❌ EPS test error: {e}")
        eps_success = False
    finally:
        # Clean up
        try:
            os.unlink(eps_file)
        except:
            pass
    
    return svg_success or eps_success

def main():
    """Main test function."""
    print("🚀 Vector File ExifTool Embedding Test")
    print("=" * 50)
    
    # Test 1: ExifTool vector format support
    format_support = test_exiftool_vector_support()
    
    if not format_support:
        print("\n❌ ExifTool does not support vector formats")
        return False
    
    # Test 2: Metadata embedding
    embedding_success = test_vector_metadata_embedding()
    
    print("\n" + "=" * 50)
    if embedding_success:
        print("✅ Vector metadata embedding tests PASSED!")
        print("🎉 ExifTool can successfully embed metadata into vector files!")
    else:
        print("❌ Vector metadata embedding tests FAILED!")
        print("⚠️ ExifTool may have limited support for vector metadata embedding")
    
    return embedding_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
