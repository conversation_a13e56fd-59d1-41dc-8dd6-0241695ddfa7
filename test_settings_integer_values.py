#!/usr/bin/env python3
"""
Test script to verify that settings window scales show integer values only.
This creates a test window with the same scale configuration as Meta Master.
"""

import tkinter as tk
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import sys
import os

def create_test_integer_scales_window():
    """Create a test window with integer-only scales to verify the fix."""
    
    # Create main window
    root = ttk.Window(themename="darkly")
    root.title("Settings Integer Values Test")
    root.geometry("800x600")
    root.resizable(True, True)
    
    # Create main frame with scrolling
    main_container = ttk.Frame(root)
    main_container.pack(fill="both", expand=True, padx=10, pady=10)

    # Title section
    title_section = ttk.LabelFrame(main_container, text="Title Settings (Integer Values Test)", padding="10")
    title_section.pack(fill="x", pady=5)
    title_section.columnconfigure(1, weight=1)

    # Min Title Characters
    min_title_label = ttk.Label(title_section, text="Minimum Title Characters:", width=25)
    min_title_label.grid(row=0, column=0, sticky="w", padx=5, pady=5)
    min_title_var = tk.IntVar(value=85)
    min_title_scale = ttk.Scale(
        title_section,
        from_=80,
        to=100,
        variable=min_title_var,
        length=300,
        bootstyle="success",
        command=lambda val: min_title_var.set(int(float(val)))
    )
    min_title_scale.grid(row=0, column=1, sticky="ew", padx=5, pady=5)
    min_title_value_label = ttk.Label(title_section, textvariable=min_title_var, width=4)
    min_title_value_label.grid(row=0, column=2, sticky="w", padx=5, pady=5)

    # Max Title Characters
    max_title_label = ttk.Label(title_section, text="Maximum Title Characters:", width=25)
    max_title_label.grid(row=1, column=0, sticky="w", padx=5, pady=5)
    max_title_var = tk.IntVar(value=150)
    max_title_scale = ttk.Scale(
        title_section,
        from_=100,
        to=200,
        variable=max_title_var,
        length=300,
        bootstyle="danger",
        command=lambda val: max_title_var.set(int(float(val)))
    )
    max_title_scale.grid(row=1, column=1, sticky="ew", padx=5, pady=5)
    max_title_value_label = ttk.Label(title_section, textvariable=max_title_var, width=4)
    max_title_value_label.grid(row=1, column=2, sticky="w", padx=5, pady=5)

    # Keywords section
    keywords_section = ttk.LabelFrame(main_container, text="Keywords Settings (Integer Values Test)", padding="10")
    keywords_section.pack(fill="x", pady=5)
    keywords_section.columnconfigure(1, weight=1)

    # Min Keywords
    min_keywords_label = ttk.Label(keywords_section, text="Minimum Keywords:", width=25)
    min_keywords_label.grid(row=0, column=0, sticky="w", padx=5, pady=5)
    min_keywords_var = tk.IntVar(value=25)
    min_keywords_scale = ttk.Scale(
        keywords_section,
        from_=10,
        to=40,
        variable=min_keywords_var,
        length=300,
        bootstyle="success",
        command=lambda val: min_keywords_var.set(int(float(val)))
    )
    min_keywords_scale.grid(row=0, column=1, sticky="ew", padx=5, pady=5)
    min_keywords_value_label = ttk.Label(keywords_section, textvariable=min_keywords_var, width=4)
    min_keywords_value_label.grid(row=0, column=2, sticky="w", padx=5, pady=5)

    # Max Keywords
    max_keywords_label = ttk.Label(keywords_section, text="Maximum Keywords:", width=25)
    max_keywords_label.grid(row=1, column=0, sticky="w", padx=5, pady=5)
    max_keywords_var = tk.IntVar(value=35)
    max_keywords_scale = ttk.Scale(
        keywords_section,
        from_=20,
        to=50,
        variable=max_keywords_var,
        length=300,
        bootstyle="danger",
        command=lambda val: max_keywords_var.set(int(float(val)))
    )
    max_keywords_scale.grid(row=1, column=1, sticky="ew", padx=5, pady=5)
    max_keywords_value_label = ttk.Label(keywords_section, textvariable=max_keywords_var, width=4)
    max_keywords_value_label.grid(row=1, column=2, sticky="w", padx=5, pady=5)

    # Description section
    description_section = ttk.LabelFrame(main_container, text="Description Settings (Integer Values Test)", padding="10")
    description_section.pack(fill="x", pady=5)
    description_section.columnconfigure(1, weight=1)

    # Min Description Characters
    min_description_label = ttk.Label(description_section, text="Minimum Description Characters:", width=25)
    min_description_label.grid(row=0, column=0, sticky="w", padx=5, pady=5)
    min_description_var = tk.IntVar(value=90)
    min_description_scale = ttk.Scale(
        description_section,
        from_=80,
        to=100,
        variable=min_description_var,
        length=300,
        bootstyle="success",
        command=lambda val: min_description_var.set(int(float(val)))
    )
    min_description_scale.grid(row=0, column=1, sticky="ew", padx=5, pady=5)
    min_description_value_label = ttk.Label(description_section, textvariable=min_description_var, width=4)
    min_description_value_label.grid(row=0, column=2, sticky="w", padx=5, pady=5)

    # Max Description Characters
    max_description_label = ttk.Label(description_section, text="Maximum Description Characters:", width=25)
    max_description_label.grid(row=1, column=0, sticky="w", padx=5, pady=5)
    max_description_var = tk.IntVar(value=150)
    max_description_scale = ttk.Scale(
        description_section,
        from_=100,
        to=200,
        variable=max_description_var,
        length=300,
        bootstyle="danger",
        command=lambda val: max_description_var.set(int(float(val)))
    )
    max_description_scale.grid(row=1, column=1, sticky="ew", padx=5, pady=5)
    max_description_value_label = ttk.Label(description_section, textvariable=max_description_var, width=4)
    max_description_value_label.grid(row=1, column=2, sticky="w", padx=5, pady=5)

    # Test section with comparison
    test_section = ttk.LabelFrame(main_container, text="Comparison Test", padding="10")
    test_section.pack(fill="x", pady=5)
    test_section.columnconfigure(1, weight=1)

    # Scale WITHOUT integer command (shows decimals)
    decimal_label = ttk.Label(test_section, text="Scale WITHOUT integer fix:", width=25)
    decimal_label.grid(row=0, column=0, sticky="w", padx=5, pady=5)
    decimal_var = tk.DoubleVar(value=50.5)
    decimal_scale = ttk.Scale(
        test_section,
        from_=0,
        to=100,
        variable=decimal_var,
        length=300,
        bootstyle="warning"
        # NO command parameter - will show decimals
    )
    decimal_scale.grid(row=0, column=1, sticky="ew", padx=5, pady=5)
    decimal_value_label = ttk.Label(test_section, textvariable=decimal_var, width=8)
    decimal_value_label.grid(row=0, column=2, sticky="w", padx=5, pady=5)

    # Scale WITH integer command (shows integers only)
    integer_label = ttk.Label(test_section, text="Scale WITH integer fix:", width=25)
    integer_label.grid(row=1, column=0, sticky="w", padx=5, pady=5)
    integer_var = tk.IntVar(value=50)
    integer_scale = ttk.Scale(
        test_section,
        from_=0,
        to=100,
        variable=integer_var,
        length=300,
        bootstyle="info",
        command=lambda val: integer_var.set(int(float(val)))
    )
    integer_scale.grid(row=1, column=1, sticky="ew", padx=5, pady=5)
    integer_value_label = ttk.Label(test_section, textvariable=integer_var, width=8)
    integer_value_label.grid(row=1, column=2, sticky="w", padx=5, pady=5)

    # Instructions
    instructions_section = ttk.LabelFrame(main_container, text="Test Instructions", padding="10")
    instructions_section.pack(fill="x", pady=5)
    
    instructions_text = """
🔧 Test Instructions:
1. Move the scales above and observe the values displayed on the right
2. The top scales (Title, Keywords, Description) should show ONLY whole numbers (1, 2, 50, 99, etc.)
3. The comparison section shows the difference:
   • Orange scale: Shows decimals (50.5, 75.3, etc.) - OLD behavior
   • Blue scale: Shows integers only (50, 75, etc.) - NEW behavior
4. All Meta Master settings scales should now behave like the blue scale

✅ Expected Results:
• Title scales: Show integers from 80-100 and 100-200
• Keywords scales: Show integers from 10-40 and 20-50  
• Description scales: Show integers from 80-100 and 100-200
• No decimal values should appear in the value labels
"""
    
    instructions_label = ttk.Label(
        instructions_section, 
        text=instructions_text,
        justify="left",
        font=("Segoe UI", 9)
    )
    instructions_label.pack(anchor="w")

    # Close button
    close_button = ttk.Button(
        main_container,
        text="Close Test Window",
        command=root.destroy,
        bootstyle=DANGER
    )
    close_button.pack(pady=10)
    
    return root

def test_integer_values():
    """Test the integer values functionality."""
    print("🚀 Testing Settings Integer Values")
    print("=" * 50)
    
    print("📋 Test Purpose:")
    print("Verify that all settings window scales show whole numbers only")
    print("instead of decimal values like 85.5, 127.3, etc.")
    
    print("\n🔧 What was fixed:")
    print("• Added command=lambda val: var.set(int(float(val))) to all Scale widgets")
    print("• This forces the scale to always set integer values")
    print("• IntVar variables ensure the displayed values are whole numbers")
    
    print("\n🖱️ Opening test window...")
    
    try:
        root = create_test_integer_scales_window()
        
        print("✅ Test window created successfully!")
        print("🎯 Move the scales to verify they show only whole numbers")
        
        # Start the GUI
        root.mainloop()
        
        print("✅ Test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error creating test window: {e}")
        return False

def main():
    """Main test function."""
    print("🔢 Settings Integer Values Test")
    print("=" * 50)
    
    success = test_integer_values()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Integer values test completed!")
        print("🎉 Settings scales should now show whole numbers only")
        print("\n📋 Changes made:")
        print("   • Min Title Characters: 80-100 (integers only)")
        print("   • Max Title Characters: 100-200 (integers only)")
        print("   • Min Keywords: 10-40 (integers only)")
        print("   • Max Keywords: 20-50 (integers only)")
        print("   • Min Description Characters: 80-100 (integers only)")
        print("   • Max Description Characters: 100-200 (integers only)")
        print("\n🔧 Technical fix:")
        print("   • Added command callbacks to force integer values")
        print("   • Used IntVar variables for proper integer handling")
        print("   • Eliminated decimal display in scale values")
    else:
        print("❌ Integer values test failed!")
        print("⚠️ Check the error messages above for details")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
