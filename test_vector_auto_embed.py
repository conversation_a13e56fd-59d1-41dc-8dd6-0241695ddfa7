#!/usr/bin/env python3
"""
Test script to verify the new vector file auto-embed functionality.
This tests the integration of ExifTool vector embedding with the auto-embed system.
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add the current directory to Python path to import from Meta Master
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_eps():
    """Create a simple test EPS file."""
    eps_content = '''%!PS-Adobe-3.0 EPSF-3.0
%%BoundingBox: 0 0 100 100
%%Creator: Meta Master Test
%%Title: Test EPS File
%%EndComments
newpath
50 50 40 0 360 arc
1 0 0 setrgbcolor
fill
showpage
%%EOF'''
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.eps', delete=False, encoding='utf-8') as f:
        f.write(eps_content)
        return f.name

def create_test_ai():
    """Create a simple test AI file (similar to EPS)."""
    ai_content = '''%!PS-Adobe-3.0 
%%Creator: Adobe Illustrator(R) 24.0
%%AI5_FileFormat 14.0
%%Title: Test AI File
%%BoundingBox: 0 0 100 100
%%EndComments
newpath
50 50 30 0 360 arc
0 1 0 setrgbcolor
fill
showpage
%%EOF'''
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.ai', delete=False, encoding='utf-8') as f:
        f.write(ai_content)
        return f.name

def create_test_svg():
    """Create a simple test SVG file."""
    svg_content = '''<?xml version="1.0" encoding="UTF-8"?>
<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
  <circle cx="50" cy="50" r="40" stroke="black" stroke-width="3" fill="blue" />
  <text x="50" y="55" text-anchor="middle" fill="white" font-family="Arial" font-size="12">SVG</text>
</svg>'''
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.svg', delete=False, encoding='utf-8') as f:
        f.write(svg_content)
        return f.name

def test_embed_metadata_function():
    """Test the embed_metadata function directly with vector files."""
    print("🔧 Testing embed_metadata function with vector files...")
    
    # Import the function from Meta Master
    try:
        from importlib import import_module
        meta_master = import_module('Meta Master')
        embed_metadata = meta_master.embed_metadata
    except Exception as e:
        print(f"❌ Could not import embed_metadata function: {e}")
        return False
    
    # Test data
    title = "Test Vector Metadata"
    description = "This is a test description for vector metadata embedding"
    keywords = ["vector", "test", "metadata", "exiftool"]
    
    results = {}
    
    # Test EPS file
    print("\n📄 Testing EPS file...")
    eps_file = create_test_eps()
    try:
        result = embed_metadata(eps_file, title, description, keywords)
        results['EPS'] = result
        print(f"   EPS result: {'✅ Success' if result else '❌ Failed'}")
    except Exception as e:
        print(f"   ❌ EPS error: {e}")
        results['EPS'] = False
    finally:
        try:
            os.unlink(eps_file)
        except:
            pass
    
    # Test AI file
    print("\n📄 Testing AI file...")
    ai_file = create_test_ai()
    try:
        result = embed_metadata(ai_file, title, description, keywords)
        results['AI'] = result
        print(f"   AI result: {'✅ Success' if result else '❌ Failed'}")
    except Exception as e:
        print(f"   ❌ AI error: {e}")
        results['AI'] = False
    finally:
        try:
            os.unlink(ai_file)
        except:
            pass
    
    # Test SVG file (should fail gracefully)
    print("\n📄 Testing SVG file (should fail gracefully)...")
    svg_file = create_test_svg()
    try:
        result = embed_metadata(svg_file, title, description, keywords)
        results['SVG'] = result
        if not result:
            print(f"   ✅ SVG correctly rejected (read-only format)")
        else:
            print(f"   ⚠️ SVG unexpectedly succeeded")
    except Exception as e:
        print(f"   ❌ SVG error: {e}")
        results['SVG'] = False
    finally:
        try:
            os.unlink(svg_file)
        except:
            pass
    
    return results

def test_auto_embed_integration():
    """Test the auto_embed_metadata function with vector files."""
    print("\n🔧 Testing auto_embed_metadata integration...")
    
    # Import the function from Meta Master
    try:
        from importlib import import_module
        meta_master = import_module('Meta Master')
        auto_embed_metadata = meta_master.auto_embed_metadata
    except Exception as e:
        print(f"❌ Could not import auto_embed_metadata function: {e}")
        return False
    
    # Test data
    title = "Auto Embed Test Vector"
    description = "This is a test for auto-embed functionality"
    keywords = "vector,auto,embed,test"
    
    results = {}
    
    # Test EPS file
    print("\n📄 Testing EPS auto-embed...")
    eps_file = create_test_eps()
    try:
        result = auto_embed_metadata(eps_file, title, keywords, description)
        results['EPS_auto'] = result
        print(f"   EPS auto-embed result: {'✅ Success' if result else '❌ Failed'}")
    except Exception as e:
        print(f"   ❌ EPS auto-embed error: {e}")
        results['EPS_auto'] = False
    finally:
        try:
            os.unlink(eps_file)
        except:
            pass
    
    # Test AI file
    print("\n📄 Testing AI auto-embed...")
    ai_file = create_test_ai()
    try:
        result = auto_embed_metadata(ai_file, title, keywords, description)
        results['AI_auto'] = result
        print(f"   AI auto-embed result: {'✅ Success' if result else '❌ Failed'}")
    except Exception as e:
        print(f"   ❌ AI auto-embed error: {e}")
        results['AI_auto'] = False
    finally:
        try:
            os.unlink(ai_file)
        except:
            pass
    
    return results

def test_file_type_detection():
    """Test file type detection and validation."""
    print("\n🔧 Testing file type detection...")
    
    # Create test files
    eps_file = create_test_eps()
    ai_file = create_test_ai()
    svg_file = create_test_svg()
    
    test_files = {
        'EPS': eps_file,
        'AI': ai_file,
        'SVG': svg_file
    }
    
    results = {}
    
    for file_type, file_path in test_files.items():
        ext = os.path.splitext(file_path)[1].lower()
        is_vector = ext in ['.eps', '.ai', '.svg']
        is_writable = ext in ['.eps', '.ai']
        
        print(f"   📄 {file_type} ({ext}): Vector={is_vector}, Writable={is_writable}")
        results[file_type] = {'vector': is_vector, 'writable': is_writable}
        
        # Clean up
        try:
            os.unlink(file_path)
        except:
            pass
    
    return results

def main():
    """Main test function."""
    print("🚀 Vector Auto-Embed Integration Test")
    print("=" * 50)
    
    # Test 1: File type detection
    print("Test 1: File Type Detection")
    detection_results = test_file_type_detection()
    
    # Test 2: Direct embed_metadata function
    print("\nTest 2: Direct embed_metadata Function")
    embed_results = test_embed_metadata_function()
    
    # Test 3: Auto-embed integration
    print("\nTest 3: Auto-Embed Integration")
    auto_embed_results = test_auto_embed_integration()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    print("\n🔍 File Type Detection:")
    for file_type, result in detection_results.items():
        print(f"   {file_type}: Vector={result['vector']}, Writable={result['writable']}")
    
    print("\n🔧 Direct Embedding:")
    for file_type, result in embed_results.items():
        status = "✅ Success" if result else "❌ Failed" if file_type != 'SVG' else "✅ Correctly Rejected"
        print(f"   {file_type}: {status}")
    
    print("\n🤖 Auto-Embed:")
    for file_type, result in auto_embed_results.items():
        status = "✅ Success" if result else "❌ Failed"
        print(f"   {file_type}: {status}")
    
    # Overall assessment
    eps_success = embed_results.get('EPS', False) and auto_embed_results.get('EPS_auto', False)
    ai_success = embed_results.get('AI', False) and auto_embed_results.get('AI_auto', False)
    svg_handled = not embed_results.get('SVG', True)  # Should be False (rejected)
    
    overall_success = eps_success and ai_success and svg_handled
    
    print(f"\n🎯 Overall Result: {'✅ SUCCESS' if overall_success else '❌ FAILED'}")
    
    if overall_success:
        print("🎉 Vector file auto-embed functionality is working correctly!")
        print("   • EPS files: Metadata embedding supported")
        print("   • AI files: Metadata embedding supported") 
        print("   • SVG files: Correctly identified as read-only")
    else:
        print("⚠️ Some issues detected with vector file auto-embed functionality")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
