#!/usr/bin/env python3
"""
Test script to verify PNG file detection and button state management.
"""

import os
import sys
import tempfile
from PIL import Image

def simulate_image_listbox():
    """Simulate the image_listbox functionality for testing."""
    class MockListbox:
        def __init__(self):
            self.items = []
        
        def size(self):
            return len(self.items)
        
        def get(self, index):
            return self.items[index]
        
        def insert(self, index, item):
            if index == "END" or index == -1:
                self.items.append(item)
            else:
                self.items.insert(index, item)
        
        def delete(self, start, end=None):
            if start == 0 and end == "END":
                self.items.clear()
            elif end is None:
                del self.items[start]
            else:
                del self.items[start:end+1]
    
    return MockListbox()

def simulate_png_buttons():
    """Simulate PNG button functionality for testing."""
    class MockButton:
        def __init__(self, name):
            self.name = name
            self.state = "normal"
        
        def config(self, state=None, **kwargs):
            if state:
                self.state = state
                print(f"🔧 {self.name} button state: {state}")
    
    return <PERSON>ck<PERSON>utton("PNG Isolated"), <PERSON>ck<PERSON>utton("PNG Refine")

def check_png_files_selected_simulation(image_listbox, png_isolated_btn, png_refine_btn):
    """Simulate the check_png_files_selected function."""
    has_png_files = False
    
    # Check if any PNG files are in the image_listbox
    for i in range(image_listbox.size()):
        file_path = image_listbox.get(i)
        if file_path.lower().endswith('.png'):
            has_png_files = True
            break
    
    # Enable or disable PNG buttons based on PNG file presence
    if has_png_files:
        png_isolated_btn.config(state="normal")
        png_refine_btn.config(state="normal")
        print("✅ PNG files detected - PNG options enabled")
    else:
        png_isolated_btn.config(state="disabled")
        png_refine_btn.config(state="disabled")
        print("❌ No PNG files detected - PNG options disabled")
    
    return has_png_files

def test_png_detection_scenarios():
    """Test various PNG file detection scenarios."""
    print("🔧 Testing PNG File Detection Scenarios...")
    
    # Create mock objects
    image_listbox = simulate_image_listbox()
    png_isolated_btn, png_refine_btn = simulate_png_buttons()
    
    # Test scenarios
    scenarios = [
        {
            "name": "No files selected",
            "files": [],
            "expected_png": False,
            "expected_state": "disabled"
        },
        {
            "name": "Only JPG files",
            "files": ["image1.jpg", "image2.jpeg", "photo.JPG"],
            "expected_png": False,
            "expected_state": "disabled"
        },
        {
            "name": "Only PNG files",
            "files": ["image1.png", "image2.PNG", "photo.png"],
            "expected_png": True,
            "expected_state": "normal"
        },
        {
            "name": "Mixed files with PNG",
            "files": ["image1.jpg", "image2.png", "photo.jpeg", "graphic.PNG"],
            "expected_png": True,
            "expected_state": "normal"
        },
        {
            "name": "Vector files only",
            "files": ["vector1.svg", "vector2.eps", "design.ai"],
            "expected_png": False,
            "expected_state": "disabled"
        },
        {
            "name": "Video files only",
            "files": ["video1.mp4", "video2.mov", "clip.avi"],
            "expected_png": False,
            "expected_state": "disabled"
        },
        {
            "name": "Mixed with one PNG",
            "files": ["video.mp4", "vector.svg", "image.jpg", "graphic.png"],
            "expected_png": True,
            "expected_state": "normal"
        }
    ]
    
    all_passed = True
    
    for scenario in scenarios:
        print(f"\n📋 Testing: {scenario['name']}")
        print(f"   Files: {scenario['files']}")
        
        # Clear and populate listbox
        image_listbox.delete(0, "END")
        for file in scenario['files']:
            image_listbox.insert("END", file)
        
        # Run detection
        has_png = check_png_files_selected_simulation(image_listbox, png_isolated_btn, png_refine_btn)
        
        # Verify results
        if has_png == scenario['expected_png'] and png_isolated_btn.state == scenario['expected_state']:
            print(f"   ✅ PASSED: PNG detected={has_png}, Button state={png_isolated_btn.state}")
        else:
            print(f"   ❌ FAILED: Expected PNG={scenario['expected_png']}, Got={has_png}")
            print(f"            Expected state={scenario['expected_state']}, Got={png_isolated_btn.state}")
            all_passed = False
    
    return all_passed

def test_file_selection_integration():
    """Test integration with file selection functions."""
    print("\n🔧 Testing File Selection Integration...")
    
    # Simulate file selection scenarios
    selection_scenarios = [
        {
            "function": "select_images",
            "description": "Image selection with PNG files",
            "files": ["photo1.jpg", "graphic.png", "image.jpeg"],
            "should_enable_png": True
        },
        {
            "function": "select_vectors", 
            "description": "Vector selection (no PNG)",
            "files": ["design.svg", "logo.eps", "vector.ai"],
            "should_enable_png": False
        },
        {
            "function": "select_videos",
            "description": "Video selection (no PNG)",
            "files": ["clip1.mp4", "video.mov", "movie.avi"],
            "should_enable_png": False
        },
        {
            "function": "select_folder",
            "description": "Folder selection with mixed files including PNG",
            "files": ["doc.pdf", "image.png", "video.mp4", "design.svg"],
            "should_enable_png": True
        }
    ]
    
    image_listbox = simulate_image_listbox()
    png_isolated_btn, png_refine_btn = simulate_png_buttons()
    
    all_passed = True
    
    for scenario in selection_scenarios:
        print(f"\n📋 Testing: {scenario['function']} - {scenario['description']}")
        
        # Simulate file selection
        image_listbox.delete(0, "END")
        for file in scenario['files']:
            image_listbox.insert("END", file)
        
        # Run PNG detection (simulating what happens after file selection)
        has_png = check_png_files_selected_simulation(image_listbox, png_isolated_btn, png_refine_btn)
        
        expected_state = "normal" if scenario['should_enable_png'] else "disabled"
        
        if has_png == scenario['should_enable_png'] and png_isolated_btn.state == expected_state:
            print(f"   ✅ PASSED: PNG buttons correctly {'enabled' if scenario['should_enable_png'] else 'disabled'}")
        else:
            print(f"   ❌ FAILED: Expected PNG buttons {'enabled' if scenario['should_enable_png'] else 'disabled'}")
            all_passed = False
    
    return all_passed

def test_clear_data_integration():
    """Test PNG button state when data is cleared."""
    print("\n🔧 Testing Clear Data Integration...")
    
    image_listbox = simulate_image_listbox()
    png_isolated_btn, png_refine_btn = simulate_png_buttons()
    
    # First, add some PNG files to enable buttons
    image_listbox.insert("END", "image1.png")
    image_listbox.insert("END", "image2.jpg")
    
    print("📋 Before clear - adding PNG files:")
    has_png_before = check_png_files_selected_simulation(image_listbox, png_isolated_btn, png_refine_btn)
    
    # Simulate clear data
    print("\n📋 Simulating clear data:")
    image_listbox.delete(0, "END")
    has_png_after = check_png_files_selected_simulation(image_listbox, png_isolated_btn, png_refine_btn)
    
    if has_png_before and not has_png_after and png_isolated_btn.state == "disabled":
        print("✅ PASSED: PNG buttons correctly disabled after clear")
        return True
    else:
        print("❌ FAILED: PNG buttons not properly disabled after clear")
        return False

def test_edge_cases():
    """Test edge cases for PNG detection."""
    print("\n🔧 Testing Edge Cases...")
    
    image_listbox = simulate_image_listbox()
    png_isolated_btn, png_refine_btn = simulate_png_buttons()
    
    edge_cases = [
        {
            "name": "Case sensitivity",
            "files": ["image.PNG", "photo.Png", "graphic.pNg"],
            "expected_png": True
        },
        {
            "name": "Files with PNG in name but different extension",
            "files": ["png_image.jpg", "my_png_file.jpeg"],
            "expected_png": False
        },
        {
            "name": "Empty file list",
            "files": [],
            "expected_png": False
        },
        {
            "name": "Files with no extension",
            "files": ["image", "photo", "graphic"],
            "expected_png": False
        },
        {
            "name": "Files with multiple dots",
            "files": ["image.backup.png", "photo.old.jpg"],
            "expected_png": True
        }
    ]
    
    all_passed = True
    
    for case in edge_cases:
        print(f"\n📋 Testing edge case: {case['name']}")
        
        image_listbox.delete(0, "END")
        for file in case['files']:
            image_listbox.insert("END", file)
        
        has_png = check_png_files_selected_simulation(image_listbox, png_isolated_btn, png_refine_btn)
        
        if has_png == case['expected_png']:
            print(f"   ✅ PASSED: Correctly detected PNG={has_png}")
        else:
            print(f"   ❌ FAILED: Expected PNG={case['expected_png']}, Got={has_png}")
            all_passed = False
    
    return all_passed

def main():
    """Run all PNG file detection tests."""
    print("🚀 Testing PNG File Detection and Button State Management")
    print("=" * 60)
    
    all_tests_passed = True
    
    # Test PNG detection scenarios
    if not test_png_detection_scenarios():
        all_tests_passed = False
    
    # Test file selection integration
    if not test_file_selection_integration():
        all_tests_passed = False
    
    # Test clear data integration
    if not test_clear_data_integration():
        all_tests_passed = False
    
    # Test edge cases
    if not test_edge_cases():
        all_tests_passed = False
    
    print("\n" + "=" * 60)
    if all_tests_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ PNG file detection and button state management working correctly")
        
        print("\n💡 Implementation Summary:")
        print("   ✅ PNG Detection Function:")
        print("      - Checks all files in image_listbox for .png extension")
        print("      - Case-insensitive detection")
        print("      - Enables/disables PNG buttons accordingly")
        
        print("\n   ✅ Integration Points:")
        print("      - select_images() - Enables PNG options if PNG files selected")
        print("      - select_vectors() - Disables PNG options (no PNG in vectors)")
        print("      - select_videos() - Disables PNG options (no PNG in videos)")
        print("      - select_folder() - Enables PNG options if folder contains PNG")
        print("      - clear_data() - Disables PNG options when files cleared")
        
        print("\n   ✅ Button States:")
        print("      - PNG Isolated button: Enabled only when PNG files present")
        print("      - Refine PNG BG button: Enabled only when PNG files present")
        print("      - Both buttons disabled by default and when no PNG files")
        
        print("\n🎯 User Experience:")
        print("   - PNG options only appear when relevant (PNG files selected)")
        print("   - Clear visual feedback about when PNG features are available")
        print("   - Prevents confusion when working with non-PNG files")
        
    else:
        print("❌ Some tests failed!")
        print("🔧 Check the issues above")
    
    return all_tests_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
